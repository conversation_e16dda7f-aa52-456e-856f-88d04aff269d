Provide some refactoring. Consider fixing the following points:
1. Decide where should the VideoStreamConverter::VideoProcessingConfig parseVideoProcessingConfig be implemented. its header located in the file not related by the name with the file containing implementation, adding additional pollution to the addon.cpp
2. I don't like the method name "createVideoConverter". Suggest a better name, involving the mention of the IQ data format, and handling with it as the video source, and of corse producing a stream. Rename the correspond methods and classes as well, rename the related methods, variables and texts in errors (in a few words - all mention of old name)
3. In "src/video-converter/video_stream_converter.cpp" (use its new name after step 2) consider following
- pass the FrameCallback as a reference "&", and store it as a reference in the class
- move the AcquisitionWorker to the src/video-converter/... to be a part of video-converter
- AcquisitionWorker<uint32_t>::Config calculateWorkerConfig(const VideoProcessingConfig& config); should be the part of AcquisitionWorker, not the VideoStreamConverter.
- The calculateWorkerConfig must accept its own type of structure, having what it require, then the VideoProcessingConfig must extend that config to ensure smooth usage. VideoProcessingConfig should collect other types and be dependent on them by extending, not vice versa. After moving the methods, don't forget to delete the old ones.
- Remove
