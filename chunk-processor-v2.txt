ChunkProcessor Design & Implementation Artifact
1. Original User Requirements
Build a ChunkProcessor capable of receiving data in chunks of size X and returning it in chunks of size Y with an overlap/offset OF.
Simplicity is paramount – implementation should be “stupid simple.”
Follow a zero-copy strategy (minimal copying; details provided later).
Constructor signature:
ChunkProcessor(size_t writeChunkSize,
               size_t readChunkSize,
               size_t readOverlapSize,
               size_t numOfWriteChunks,
               std::function<void(uint32_t*, size_t)> readChunkHandler);

writeChunkSize – size (in samples) of each write operation
readChunkSize – size (in samples) of each read window
readOverlapSize – overlap between consecutive read windows
numOfWriteChunks – number of write chunks that fit into the internal buffer
readChunkHandler – callback fired synchronously when enough data is available. Receives pointer + size.
Write processing has two explicit steps:
getWriteChunkPtr() – returns pointer to next free write chunk.
commitWriteChunk() – confirms data was written.
As soon as enough data exists to satisfy a read window, invoke read<PERSON>hunkHandler as many times as needed. Pointer is valid only for the duration of the callback.
When a read window would wrap around the end of the buffer, copy the data into a pre-allocated readGlueBuffer (size = readChunkSize) and pass that pointer instead.
Use 64-bit counters to avoid overflow.
Public API: only the constructor, getWriteChunkPtr, and commitWriteChunk; all reads via the callback.
Example behaviour (WC=1024, RC=200, OV=50): first commit triggers 6 windows: [0–199] [150–349] [300–499] [450–649] [600–799] [750–949]
Another example (WC=1400, RC=3000, OV=500): third commit triggers first window [0–2999]; fifth commit triggers [2500–5499].
Buffer sizing rule: writeChunkSize * numOfWriteChunks must exceed readChunkSize + readOverlapSize.
2. Assistant’s Clarifying Questions
Are sizes expressed in 32-bit samples or bytes?
Is the read-stride formula readStride = readChunkSize − readOverlapSize correct?
Threading: single-threaded? Does callback run synchronously inside commitWriteChunk()?
What should happen if the ring overflows?
Lifetime of the glue buffer?
Need for reset capability?
Preferred error-handling style (assert vs throw)?
Alignment requirements for Raspberry Pi 5?
Maximum expected buffer sizes?
Should the class collect statistics?
3. User’s Answers
uint32_t* is a data pointer; all size_t parameters are counts of elements (samples).
Yes, the readStride formula is correct.
Single-threaded; callback executed synchronously within commitWriteChunk().
Overflow is architecturally impossible – producer always consumes immediately after writing.
Glue buffer: allocate once in ctor; free in dtor.
No reset needed; recreate the object instead.
Use assertions for argument validation.
Unsure; choose what is best for Raspberry Pi 5.
Buffers will only be a few megabytes – no need for giant-size protection.
No statistics collection for now.
Additional rule: writeChunkSize * numOfWriteChunks > readChunkSize + readOverlapSize (enforced via assert).
4. Consolidated Technical Specification & Reference Implementation
4.1 Overview

ChunkProcessor is a single-producer/single-consumer ring buffer delivering overlapping read windows with minimal copying. It allocates exactly two heap blocks for its lifetime: the main ring buffer and a glue buffer for the occasional wrap-around case.

4.2 Public Interface
class ChunkProcessor {
public:
    using ReadHandler = std::function<void(uint32_t* /*ptr*/, size_t /*count*/)>;

    ChunkProcessor(size_t writeChunkSize,
                   size_t readChunkSize,
                   size_t readOverlapSize,
                   size_t numOfWriteChunks,
                   ReadHandler readChunkHandler);

    uint32_t* getWriteChunkPtr() noexcept;   // Step-1
    void      commitWriteChunk()  noexcept;  // Step-2

    ChunkProcessor(const ChunkProcessor&)            = delete;
    ChunkProcessor& operator=(const ChunkProcessor&) = delete;
    ~ChunkProcessor() = default;
};


All sizes are counts of 32-bit samples.

4.3 Construction-time Assertions
assert(writeChunkSize  > 0);
assert(readChunkSize   > 0);
assert(numOfWriteChunks >= 2);
assert(readOverlapSize <  readChunkSize);
assert(writeChunkSize * numOfWriteChunks >= readChunkSize + readOverlapSize);

4.4 Internal State (sizes are size_t unless noted)
Variable	Meaning
mainBufSamples	writeChunkSize * numOfWriteChunks
writeHeadAbs	Absolute sample index after latest committed write (64-bit)
nextReadAbs	Absolute start index of next read window (64-bit)
readStride	readChunkSize − readOverlapSize
mainBuffer	unique_ptr<uint32_t[]> main ring
glueBuffer	unique_ptr<uint32_t[]> glue region
4.5 Algorithms

getWriteChunkPtr

return mainBuffer.get() + (writeHeadAbs % mainBufSamples);


commitWriteChunk

writeHeadAbs += writeChunkSize;
while (writeHeadAbs - nextReadAbs >= readChunkSize) {
    size_t start = nextReadAbs % mainBufSamples;
    uint32_t* out;
    if (start + readChunkSize <= mainBufSamples) {
        out = mainBuffer.get() + start;           // zero-copy
    } else {
        size_t first = mainBufSamples - start;
        size_t second = readChunkSize - first;
        std::memcpy(glueBuffer.get(),            mainBuffer.get() + start, first  * sizeof(uint32_t));
        std::memcpy(glueBuffer.get() + first,    mainBuffer.get(),         second * sizeof(uint32_t));
        out = glueBuffer.get();                  // contiguous copy
    }
    handler(out, readChunkSize);
    nextReadAbs += readStride;
}

4.6 Reference Header-only Implementation
#ifndef CHUNK_PROCESSOR_HPP
#define CHUNK_PROCESSOR_HPP

#include <cstdint>
#include <cstddef>
#include <cassert>
#include <cstring>
#include <functional>
#include <memory>

class ChunkProcessor {
public:
    using ReadHandler = std::function<void(uint32_t*, size_t)>;

    ChunkProcessor(size_t writeChunkSize,
                   size_t readChunkSize,
                   size_t readOverlapSize,
                   size_t numOfWriteChunks,
                   ReadHandler readChunkHandler)
        : wc(writeChunkSize),
          rc(readChunkSize),
          ov(readOverlapSize),
          stride(rc - ov),
          nwc(numOfWriteChunks),
          handler(std::move(readChunkHandler)),
          mainBufSamples(wc * nwc),
          mainBuffer(new uint32_t[mainBufSamples]),
          glueBuffer(new uint32_t[rc]),
          writeHeadAbs(0),
          nextReadAbs(0)
    {
        assert(wc > 0 && rc > 0 && nwc >= 2);
        assert(ov < rc);
        assert(mainBufSamples >= rc + ov);
    }

    uint32_t* getWriteChunkPtr() noexcept {
        return mainBuffer.get() + (writeHeadAbs % mainBufSamples);
    }

    void commitWriteChunk() noexcept {
        writeHeadAbs += wc;
        while (writeHeadAbs - nextReadAbs >= rc) {
            size_t start = nextReadAbs % mainBufSamples;
            uint32_t* out;
            if (start + rc <= mainBufSamples) {
                out = mainBuffer.get() + start;
            } else {
                size_t first  = mainBufSamples - start;
                size_t second = rc - first;
                std::memcpy(glueBuffer.get(),            mainBuffer.get() + start,  first  * sizeof(uint32_t));
                std::memcpy(glueBuffer.get() + first,    mainBuffer.get(),          second * sizeof(uint32_t));
                out = glueBuffer.get();
            }
            handler(out, rc);
            nextReadAbs += stride;
        }
    }

    ChunkProcessor(const ChunkProcessor&)            = delete;
    ChunkProcessor& operator=(const ChunkProcessor&) = delete;

private:
    const size_t wc, rc, ov, stride, nwc;
    ReadHandler  handler;

    const size_t                       mainBufSamples;
    std::unique_ptr<uint32_t[]>        mainBuffer;
    std::unique_ptr<uint32_t[]>        glueBuffer;

    uint64_t writeHeadAbs;
    uint64_t nextReadAbs;
};

#endif // CHUNK_PROCESSOR_HPP

