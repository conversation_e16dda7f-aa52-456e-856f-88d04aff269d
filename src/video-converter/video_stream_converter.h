#ifndef VIDEO_STREAM_CONVERTER_H
#define VIDEO_STREAM_CONVERTER_H

#include <memory>
#include <string>
#include <atomic>
#include <functional>
#include "../pipeline/acquisition_worker.h"
#include "../iiq-stream/iiq_stream.h"

/**
 * VideoStreamConverter - IQ-to-video stream conversion orchestrator
 * 
 * Manages the complete IQ-to-video demodulation process for a single stream.
 * Simplified single-instance design without global handle management.
 * Takes an initialized IIQStream and orchestrates video processing pipeline.
 */
class VideoStreamConverter {
public:
    /**
     * Video processing configuration
     */
    struct VideoProcessingConfig {
        double centerOffsetHz;      // Frequency offset for demodulation
        std::string sliceStrategy;  // Slice strategy (e.g., "auto_ntsc")
        uint32_t frameWidthPx;      // Output frame width in pixels
        
        struct QueueDepth {
            uint32_t raw;           // Raw sample queue depth
            uint32_t demod;         // Demodulated data queue depth
            uint32_t lines;         // Video line queue depth
        } queueDepth;
    };
    
    /**
     * Frame callback function type
     * Called when a complete video frame is ready
     */
    using FrameCallback = std::function<void(const uint8_t* frameData, size_t frameSize)>;
    
    /**
     * Constructor
     * @param stream Initialized and validated IIQStream (takes ownership)
     * @param config Video processing configuration
     * @param frameCallback Callback for processed video frames
     */
    VideoStreamConverter(std::unique_ptr<IIQStream> stream, const VideoProcessingConfig& config, FrameCallback frameCallback);
    ~VideoStreamConverter();
    
    /**
     * Initialize and start video conversion
     * @return true if successful, false otherwise
     */
    bool start();
    
    /**
     * Stop video conversion gracefully
     */
    void stop();
    
    /**
     * Check if converter is running
     * @return true if running, false otherwise
     */
    bool isRunning() const;
    
    /**
     * Get conversion statistics as JSON string
     * @return Statistics JSON or empty object on error
     */
    std::string getStats() const;
    
    /**
     * Get the last error message
     * @return Last error message or empty string if no error
     */
    const std::string& lastError() const;
    
    /**
     * Validate video processing configuration
     * @param config Configuration to validate
     * @param errorMsg Output parameter for error message
     * @return true if valid, false otherwise
     */
    static bool validateConfig(const VideoProcessingConfig& config, std::string& errorMsg);

private:
    // Configuration and callback
    VideoProcessingConfig config_;
    FrameCallback frameCallback_;
    
    // Processing components
    std::unique_ptr<IIQStream> stream_;
    std::unique_ptr<AcquisitionWorker<uint32_t>> acquisitionWorker_;
    
    // State management
    std::atomic<bool> running_;
    std::atomic<bool> stopRequested_;
    std::string lastError_;
    
    /**
     * Validate configuration parameters
     * @param config Configuration to validate
     * @return true if valid, false otherwise
     */
    bool validateConfig(const VideoProcessingConfig& config);
    
    /**
     * Calculate worker configuration from video processing config
     * @param config Video processing configuration
     * @return Worker configuration for AcquisitionWorker
     */
    AcquisitionWorker<uint32_t>::Config calculateWorkerConfig(const VideoProcessingConfig& config);
    
    /**
     * Handle processed chunks from acquisition worker
     * Currently outputs to stdout as specified in strategy
     * @param data Processed chunk data
     * @param size Size of chunk in samples
     */
    void handleProcessedChunk(uint32_t* data, size_t size);
    
    /**
     * Set error message
     * @param error Error message to set
     */
    void setError(const std::string& error);
    
    // Disable copy and assignment
    VideoStreamConverter(const VideoStreamConverter&) = delete;
    VideoStreamConverter& operator=(const VideoStreamConverter&) = delete;
};

#endif // VIDEO_STREAM_CONVERTER_H
