#include "video_stream_converter.h"
#include <stdexcept>
#include <iostream>
#include <sstream>
#include <iomanip>
#include <chrono>
#include <algorithm>

VideoStreamConverter::VideoStreamConverter(std::unique_ptr<IIQStream> stream, const VideoProcessingConfig& config, FrameCallback frameCallback)
    : config_(config)
    , frameCallback_(frameCallback)
    , stream_(std::move(stream))
    , running_(false)
    , stopRequested_(false) {
}

VideoStreamConverter::~VideoStreamConverter() {
    stop();
}

bool VideoStreamConverter::start() {
    if (running_) {
        setError("Video converter is already running");
        return false;
    }
    
    try {
        // Validate configuration
        if (!validateConfig(config_)) {
            return false;
        }
        
        // Validate stream
        if (!stream_ || !stream_->isActive()) {
            setError("IQ stream is not active: " + (stream_ ? stream_->lastError() : "null stream"));
            return false;
        }
        
        // Calculate worker configuration using stream sample rate
        auto workerConfig = calculateWorkerConfig(config_);
        workerConfig.sampleRate = stream_->sampleRate();  // Use actual stream sample rate

        // Create acquisition worker
        acquisitionWorker_ = std::make_unique<AcquisitionWorker<uint32_t>>(
            std::move(stream_),
            workerConfig,
            [this](uint32_t* data, size_t size) { handleProcessedChunk(data, size); }
        );
        
        // Start the acquisition worker
        if (!acquisitionWorker_->start()) {
            setError("Failed to start acquisition worker: " + acquisitionWorker_->lastError());
            return false;
        }
        
        running_ = true;
        return true;
        
    } catch (const std::exception& e) {
        setError("Exception starting video converter: " + std::string(e.what()));
        return false;
    }
}

void VideoStreamConverter::stop() {
    if (running_.exchange(false)) {
        stopRequested_ = true;
        
        if (acquisitionWorker_) {
            acquisitionWorker_->requestStop();
            acquisitionWorker_->join();
            acquisitionWorker_.reset();
        }
        
        // Note: stream_ is now owned by acquisitionWorker_, so it's cleaned up there
        stream_.reset();
    }
}

bool VideoStreamConverter::isRunning() const {
    return running_ && acquisitionWorker_ && acquisitionWorker_->isRunning();
}

std::string VideoStreamConverter::getStats() const {
    if (!acquisitionWorker_) {
        return "{}";
    }

    try {
        // Get stats from acquisition worker and format as JSON
        auto stats = acquisitionWorker_->getStats();

        std::ostringstream json;
        json << "{"
             << "\"running\":" << (isRunning() ? "true" : "false") << ","
             << "\"totalSamplesRead\":" << stats.totalSamplesRead << ","
             << "\"totalChunksProcessed\":" << stats.totalChunksProcessed << ","
             << "\"streamErrors\":" << stats.streamErrors << ","
             << "\"averageReadRate\":" << std::fixed << std::setprecision(2) << stats.averageReadRate
             << "}";

        return json.str();

    } catch (const std::exception& e) {
        return "{\"error\":\"" + std::string(e.what()) + "\"}";
    }
}

const std::string& VideoStreamConverter::lastError() const {
    return lastError_;
}

bool VideoStreamConverter::validateConfig(const VideoProcessingConfig& config, std::string& errorMsg) {
    if (config.frameWidthPx == 0) {
        errorMsg = "Frame width cannot be zero";
        return false;
    }
    
    if (config.frameWidthPx > 4096) {
        errorMsg = "Frame width too large (max 4096 pixels)";
        return false;
    }
    
    if (config.queueDepth.raw == 0 || 
        config.queueDepth.demod == 0 || 
        config.queueDepth.lines == 0) {
        errorMsg = "Queue depths must be positive";
        return false;
    }
    
    if (config.queueDepth.raw > 1024 || 
        config.queueDepth.demod > 1024 || 
        config.queueDepth.lines > 1024) {
        errorMsg = "Queue depths too large (max 1024 each)";
        return false;
    }
    
    if (config.sliceStrategy.empty()) {
        errorMsg = "Slice strategy cannot be empty";
        return false;
    }
    
    return true;
}

bool VideoStreamConverter::validateConfig(const VideoProcessingConfig& config) {
    std::string errorMsg;
    if (!validateConfig(config, errorMsg)) {
        setError("Configuration validation failed: " + errorMsg);
        return false;
    }
    return true;
}

AcquisitionWorker<uint32_t>::Config VideoStreamConverter::calculateWorkerConfig(const VideoProcessingConfig& config) {
    AcquisitionWorker<uint32_t>::Config workerConfig;

    // Use conservative defaults to avoid assertion failures
    workerConfig.writeChunkSize = 1024;   // Smaller write chunk size
    workerConfig.readChunkSize = 1024;    // Same as write chunk size for simplicity
    workerConfig.readOverlapSize = 256;   // 25% overlap
    workerConfig.numWriteChunks = std::max(config.queueDepth.raw, static_cast<uint32_t>(4));
    workerConfig.sampleRate = 20000000;   // Default 20 MS/s (will be overridden)
    workerConfig.sliceStrategy = "fixed"; // Use fixed strategy to avoid auto calculation issues

    // Validate buffer size assertion: writeChunkSize * numWriteChunks >= readChunkSize + readOverlapSize
    size_t bufferSize = workerConfig.writeChunkSize * workerConfig.numWriteChunks;
    size_t requiredSize = workerConfig.readChunkSize + workerConfig.readOverlapSize;

    if (bufferSize < requiredSize) {
        // Increase numWriteChunks to satisfy the assertion
        workerConfig.numWriteChunks = (requiredSize + workerConfig.writeChunkSize - 1) / workerConfig.writeChunkSize;
        if (workerConfig.numWriteChunks < 2) {
            workerConfig.numWriteChunks = 2;  // Minimum required by ChunkProcessor
        }
    }

    return workerConfig;
}

void VideoStreamConverter::handleProcessedChunk(uint32_t* data, size_t size) {
    // For now, output to stdout as specified in strategy document
    // TODO: Implement actual video processing pipeline (stages 3-6)
    std::cout << "Processed chunk: " << size << " samples" << std::endl;
    
    // TODO: When video processing is implemented, call frameCallback_ with frame data
    // frameCallback_(frameData, frameSize);
}

void VideoStreamConverter::setError(const std::string& error) {
    lastError_ = error;
}
