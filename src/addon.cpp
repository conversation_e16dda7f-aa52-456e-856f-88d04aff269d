#include <node.h>
#include <v8.h>
#include <memory>
#include <fstream>
#include <iostream>
#include "stream-factory/iq_stream_factory.h"
#include "video-converter/video_stream_converter.h"

namespace VideoDecodingAddon {

using v8::Context;
using v8::Function;
using v8::FunctionCallbackInfo;
using v8::Isolate;
using v8::Local;
using v8::Object;
using v8::String;
using v8::Value;
using v8::Array;
using v8::Number;
using v8::Boolean;
using v8::Exception;
using v8::Persistent;

// Single video converter instance (simplified single-instance design)
static std::unique_ptr<VideoStreamConverter> g_videoConverter;

/**
 * Parse stream configuration from JavaScript object
 */
IQStreamFactory::StreamConfig parseStreamConfig(Isolate* isolate, Local<Object> sourceObj) {
    IQStreamFactory::StreamConfig config;
    Local<Context> context = isolate->GetCurrentContext();

    // Get type
    Local<Value> typeVal = sourceObj->Get(context, String::NewFromUtf8(isolate, "type").ToLocalChecked()).ToLocalChecked();
    if (typeVal->IsString()) {
        v8::String::Utf8Value typeStr(isolate, typeVal);
        config.type = std::string(*typeStr);
    }

    if (config.type == "wav") {
        // WAV-specific parameters
        Local<Value> pathVal = sourceObj->Get(context, String::NewFromUtf8(isolate, "path").ToLocalChecked()).ToLocalChecked();
        if (pathVal->IsString()) {
            v8::String::Utf8Value pathStr(isolate, pathVal);
            config.wav.filePath = std::string(*pathStr);
        }

        Local<Value> playModeVal = sourceObj->Get(context, String::NewFromUtf8(isolate, "playMode").ToLocalChecked()).ToLocalChecked();
        if (playModeVal->IsString()) {
            v8::String::Utf8Value playModeStr(isolate, playModeVal);
            config.wav.playMode = std::string(*playModeStr);
        } else {
            config.wav.playMode = "realtime";  // default
        }

    } else if (config.type == "bladerf") {
        // BladeRF-specific parameters
        Local<Value> serialVal = sourceObj->Get(context, String::NewFromUtf8(isolate, "serial").ToLocalChecked()).ToLocalChecked();
        if (serialVal->IsString()) {
            v8::String::Utf8Value serialStr(isolate, serialVal);
            config.bladerf.serial = std::string(*serialStr);
        }

        Local<Value> channelVal = sourceObj->Get(context, String::NewFromUtf8(isolate, "channel").ToLocalChecked()).ToLocalChecked();
        if (channelVal->IsNumber()) {
            config.bladerf.channel = channelVal->Uint32Value(context).FromJust();
        }

        Local<Value> sampleRateVal = sourceObj->Get(context, String::NewFromUtf8(isolate, "sampleRate").ToLocalChecked()).ToLocalChecked();
        if (sampleRateVal->IsNumber()) {
            config.bladerf.sampleRate = sampleRateVal->Uint32Value(context).FromJust();
        }

        Local<Value> centerHzVal = sourceObj->Get(context, String::NewFromUtf8(isolate, "centerHz").ToLocalChecked()).ToLocalChecked();
        if (centerHzVal->IsNumber()) {
            config.bladerf.centerHz = centerHzVal->NumberValue(context).FromJust();
        }

        Local<Value> bandwidthVal = sourceObj->Get(context, String::NewFromUtf8(isolate, "bandwidth").ToLocalChecked()).ToLocalChecked();
        if (bandwidthVal->IsNumber()) {
            config.bladerf.bandwidth = bandwidthVal->NumberValue(context).FromJust();
        }
    }

    return config;
}

/**
 * Parse video processing configuration from JavaScript object
 */
VideoStreamConverter::VideoProcessingConfig parseVideoProcessingConfig(Isolate* isolate, Local<Object> processingObj) {
    VideoStreamConverter::VideoProcessingConfig config;
    Local<Context> context = isolate->GetCurrentContext();
    
    // Center offset
    Local<Value> centerOffsetVal = processingObj->Get(context, String::NewFromUtf8(isolate, "centerOffsetHz").ToLocalChecked()).ToLocalChecked();
    if (centerOffsetVal->IsNumber()) {
        config.centerOffsetHz = centerOffsetVal->NumberValue(context).FromJust();
    }
    
    // Slice strategy
    Local<Value> sliceStrategyVal = processingObj->Get(context, String::NewFromUtf8(isolate, "sliceStrategy").ToLocalChecked()).ToLocalChecked();
    if (sliceStrategyVal->IsString()) {
        v8::String::Utf8Value sliceStrategyStr(isolate, sliceStrategyVal);
        config.sliceStrategy = std::string(*sliceStrategyStr);
    } else {
        config.sliceStrategy = "auto_ntsc";  // default
    }
    
    // Frame width
    Local<Value> frameWidthVal = processingObj->Get(context, String::NewFromUtf8(isolate, "frameWidthPx").ToLocalChecked()).ToLocalChecked();
    if (frameWidthVal->IsNumber()) {
        config.frameWidthPx = frameWidthVal->Uint32Value(context).FromJust();
    } else {
        config.frameWidthPx = 640;  // default
    }

    // Queue depths
    Local<Value> queueDepthVal = processingObj->Get(context, String::NewFromUtf8(isolate, "queueDepth").ToLocalChecked()).ToLocalChecked();
    if (queueDepthVal->IsObject()) {
        Local<Object> queueDepthObj = queueDepthVal.As<Object>();

        Local<Value> rawVal = queueDepthObj->Get(context, String::NewFromUtf8(isolate, "raw").ToLocalChecked()).ToLocalChecked();
        if (rawVal->IsNumber()) {
            config.queueDepth.raw = rawVal->Uint32Value(context).FromJust();
        } else {
            config.queueDepth.raw = 128;  // default
        }

        Local<Value> demodVal = queueDepthObj->Get(context, String::NewFromUtf8(isolate, "demod").ToLocalChecked()).ToLocalChecked();
        if (demodVal->IsNumber()) {
            config.queueDepth.demod = demodVal->Uint32Value(context).FromJust();
        } else {
            config.queueDepth.demod = 128;  // default
        }

        Local<Value> linesVal = queueDepthObj->Get(context, String::NewFromUtf8(isolate, "lines").ToLocalChecked()).ToLocalChecked();
        if (linesVal->IsNumber()) {
            config.queueDepth.lines = linesVal->Uint32Value(context).FromJust();
        } else {
            config.queueDepth.lines = 64;  // default
        }
    } else {
        // Use defaults
        config.queueDepth.raw = 128;
        config.queueDepth.demod = 128;
        config.queueDepth.lines = 64;
    }

    return config;
}

/**
 * Create video converter function
 * Usage: createVideoConverter(config, frameCallback)
 */
void CreateVideoConverter(const FunctionCallbackInfo<Value>& args) {
    Isolate* isolate = args.GetIsolate();
    Local<Context> context = isolate->GetCurrentContext();

    // Validate arguments
    if (args.Length() < 2 || !args[0]->IsObject() || !args[1]->IsFunction()) {
        isolate->ThrowException(Exception::TypeError(String::NewFromUtf8(isolate, "Usage: createVideoConverter(config, frameCallback)").ToLocalChecked()));
        return;
    }

    try {
        // Stop any existing converter (single-instance design)
        if (g_videoConverter) {
            g_videoConverter->stop();
            g_videoConverter.reset();
        }

        Local<Object> configObj = args[0].As<Object>();

        // Parse stream configuration
        Local<Value> sourceVal = configObj->Get(context, String::NewFromUtf8(isolate, "source").ToLocalChecked()).ToLocalChecked();
        if (!sourceVal->IsObject()) {
            isolate->ThrowException(Exception::TypeError(
                String::NewFromUtf8(isolate, "Config must have 'source' object").ToLocalChecked()));
            return;
        }

        // Parse video processing configuration
        Local<Value> processingVal = configObj->Get(context, String::NewFromUtf8(isolate, "processing").ToLocalChecked()).ToLocalChecked();
        if (!processingVal->IsObject()) {
            isolate->ThrowException(Exception::TypeError(String::NewFromUtf8(isolate, "Config must have 'processing' object").ToLocalChecked()));
            return;
        }

        auto streamConfig = parseStreamConfig(isolate, sourceVal.As<Object>());
        auto videoConfig = parseVideoProcessingConfig(isolate, processingVal.As<Object>());

        // Create IQ stream using factory (early validation in Node.js thread)
        auto stream = IQStreamFactory::createStream(streamConfig);
        if (!stream) {
            std::string error = "Failed to create IQ stream: " + IQStreamFactory::lastError();
            isolate->ThrowException(Exception::Error(String::NewFromUtf8(isolate, error.c_str()).ToLocalChecked()));
            return;
        }

        // Create frame callback (for now, just a placeholder)
        auto frameCallback = [](const uint8_t* frameData, size_t frameSize) {
            // TODO: Call JavaScript callback
            std::cout << "Frame received: " << frameSize << " bytes" << std::endl;
        };

        // Create video converter
        g_videoConverter = std::make_unique<VideoStreamConverter>(std::move(stream), videoConfig, frameCallback);

        // Start video conversion
        if (!g_videoConverter->start()) {
            std::string error = "Failed to start video converter: " + g_videoConverter->lastError();
            g_videoConverter.reset();
            isolate->ThrowException(Exception::Error(String::NewFromUtf8(isolate, error.c_str()).ToLocalChecked()));
            return;
        }

        // Return success (simplified interface - no handle ID needed)
        Local<Object> result = Object::New(isolate);
        result->Set(context, String::NewFromUtf8(isolate, "success").ToLocalChecked(), Boolean::New(isolate, true)).Check();

        args.GetReturnValue().Set(result);
    } catch (const std::exception& e) {
        isolate->ThrowException(Exception::Error(String::NewFromUtf8(isolate, ("Video converter error: " + std::string(e.what())).c_str()).ToLocalChecked()));
    }
}

/**
 * Stop video converter function
 */
void StopVideoConverter(const FunctionCallbackInfo<Value>& args) {
    Isolate* isolate = args.GetIsolate();

    if (g_videoConverter) {
        g_videoConverter->stop();
        g_videoConverter.reset();
        args.GetReturnValue().Set(Boolean::New(isolate, true));
    } else {
        args.GetReturnValue().Set(Boolean::New(isolate, false));
    }
}

/**
 * Get video converter stats function
 */
void GetVideoConverterStats(const FunctionCallbackInfo<Value>& args) {
    Isolate* isolate = args.GetIsolate();

    if (g_videoConverter) {
        std::string stats = g_videoConverter->getStats();
        args.GetReturnValue().Set(String::NewFromUtf8(isolate, stats.c_str()).ToLocalChecked());
    } else {
        args.GetReturnValue().Set(String::NewFromUtf8(isolate, "{}").ToLocalChecked());
    }
}

/**
 * Check if video converter is running
 */
void IsVideoConverterRunning(const FunctionCallbackInfo<Value>& args) {
    Isolate* isolate = args.GetIsolate();

    bool running = g_videoConverter && g_videoConverter->isRunning();
    args.GetReturnValue().Set(Boolean::New(isolate, running));
}

/**
 * Hello World function for testing
 */
void HelloWorld(const FunctionCallbackInfo<Value>& args) {
    Isolate* isolate = args.GetIsolate();
    args.GetReturnValue().Set(String::NewFromUtf8(isolate, "Hello from Video Decoding Pipeline!").ToLocalChecked());
}

/**
 * Initialize the addon
 */
void Initialize(Local<Object> exports) {
    NODE_SET_METHOD(exports, "helloWorld", HelloWorld);
    NODE_SET_METHOD(exports, "createVideoConverter", CreateVideoConverter);
    NODE_SET_METHOD(exports, "stopVideoConverter", StopVideoConverter);
    NODE_SET_METHOD(exports, "getVideoConverterStats", GetVideoConverterStats);
    NODE_SET_METHOD(exports, "isVideoConverterRunning", IsVideoConverterRunning);
}

NODE_MODULE(NODE_GYP_MODULE_NAME, Initialize)

} // namespace VideoDecodingAddon
