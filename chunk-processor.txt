Technical Specification: ChunkStreamProcessor
1. Overview

Purpose: A high-performance, zero-copy stream processing system that receives continuous data streams, buffers them efficiently, and delivers fixed-size overlapping chunks to a consumer callback.

Key Features:

Template-based for type flexibility
Zero-copy operation for contiguous chunks
Configurable overlap between consecutive chunks
Direct memory write interface for stream sources
Immediate processing upon data availability
Single-threaded, synchronous operation
2. Conceptual Model
   Data Flow
   Stream Source → [Write Buffer Region] → [Circular Buffer] → [Chunk Extraction] → Consumer Callback
   ↑                                            ↓
   └────────────────────────────────────────────┘
   (consumes data)

Chunk Layout Example

Given data stream: [0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15...]

chunk_length = 6
left_overlap = 2
chunk_stride = chunk_length - left_overlap = 4

Resulting chunks:

Chunk 0: [0,1,2,3,4,5]
Chunk 1: [4,5,6,7,8,9] (overlaps with previous by 2)
Chunk 2: [8,9,10,11,12,13]
...
3. Class Design
   template<typename T>
   class ChunkStreamProcessor {
   static_assert(std::is_trivially_copyable_v<T>,
   "T must be trivially copyable for buffer operations");

3.1 Configuration Parameters
Parameter	Type	Description	Constraints
chunk_length	size_t	Number of elements per chunk	> left_overlap
left_overlap	size_t	Number of elements overlapping with previous chunk	< chunk_length
max_write_size	size_t	Maximum elements per write operation	> 0
main_buffer_size	size_t	Total circular buffer size in elements	Calculated (see 3.5)
3.2 Internal State
private:
// Configuration
const size_t chunk_length;
const size_t left_overlap;
const size_t max_write_size;
const size_t main_buffer_size;

    // Calculated constants
    const size_t chunk_stride;      // chunk_length - left_overlap

    // Buffers
    std::vector<T> main_buffer;     // Primary circular buffer
    std::vector<T> glue_buffer;     // Secondary buffer for wraparound chunks

    // Circular buffer state
    size_t write_pos = 0;           // Next write position [0, main_buffer_size)
    size_t used_space = 0;          // Total elements currently in buffer

    // Chunk extraction state
    size_t logical_read_offset = 0; // Offset from start of used data
    bool first_chunk_pending = true;// Special handling for first chunk

    // Consumer callback
    std::function<void(const T*, size_t)> chunk_callback;

3.3 Public API
public:
// Constructor
ChunkStreamProcessor(size_t chunk_length,
size_t left_overlap,
size_t max_write_size);

    // Set the consumer callback
    void set_callback(std::function<void(const T*, size_t)> callback);

    // Get pointer and size for direct writing
    std::tuple<T*, size_t> get_write_pointer();

    // Commit written data and trigger processing
    void commit_write(size_t written_elements);

    // Query methods
    size_t get_available_space() const;
    size_t get_buffered_elements() const;
    bool can_write() const;

3.4 Core Algorithms
3.4.1 Write Pointer Calculation
std::tuple<T*, size_t> get_write_pointer() {
// Step 1: Calculate total free space
size_t free_space = main_buffer_size - used_space;
if (free_space == 0) {
return {nullptr, 0};
}

    // Step 2: Calculate contiguous space from write_pos to buffer end
    size_t space_to_end = main_buffer_size - write_pos;

    // Step 3: Determine actual writable size (minimum of three constraints)
    size_t writable_size = std::min({free_space, space_to_end, max_write_size});

    // Step 4: Return pointer and size
    return {&main_buffer[write_pos], writable_size};
}

3.4.2 Write Commit
void commit_write(size_t written_elements) {
// Preconditions
assert(written_elements <= max_write_size);
assert(used_space + written_elements <= main_buffer_size);

    // Update circular buffer state
    write_pos = (write_pos + written_elements) % main_buffer_size;
    used_space += written_elements;

    // Trigger chunk processing
    process_available_chunks();
}

3.4.3 Chunk Availability Check
bool can_form_chunk() const {
if (first_chunk_pending) {
// First chunk needs exactly chunk_length elements
return used_space >= chunk_length;
} else {
// Subsequent chunks need chunk_length elements from logical_read_offset
return (used_space - logical_read_offset) >= chunk_length;
}
}

3.4.4 Physical Read Position Calculation
size_t calculate_physical_read_pos() const {
// Convert logical offset to physical position in circular buffer
return (write_pos + main_buffer_size - used_space + logical_read_offset)
% main_buffer_size;
}

3.4.5 Chunk Extraction
const T* get_chunk_pointer() {
size_t read_pos = calculate_physical_read_pos();

    // Check if chunk is contiguous in memory
    if (read_pos + chunk_length <= main_buffer_size) {
        // Zero-copy: return direct pointer
        return &main_buffer[read_pos];
    } else {
        // Chunk wraps around: copy to glue buffer
        size_t first_part = main_buffer_size - read_pos;
        size_t second_part = chunk_length - first_part;

        std::copy_n(&main_buffer[read_pos], first_part, glue_buffer.begin());
        std::copy_n(&main_buffer[0], second_part, glue_buffer.begin() + first_part);

        return glue_buffer.data();
    }
}

3.4.6 Chunk Processing Loop
void process_available_chunks() {
while (can_form_chunk()) {
const T* chunk_ptr = get_chunk_pointer();

        // Invoke consumer callback (synchronous)
        chunk_callback(chunk_ptr, chunk_length);

        // Advance to next chunk position
        advance_to_next_chunk();
    }
}

3.4.7 Read Position Advancement
void advance_to_next_chunk() {
if (first_chunk_pending) {
// First chunk consumed its full length
first_chunk_pending = false;
logical_read_offset = chunk_stride;
used_space -= chunk_length;
} else {
// Subsequent chunks advance by stride
logical_read_offset += chunk_stride;
used_space -= chunk_stride;

        // Normalize logical offset to prevent overflow
        while (logical_read_offset >= main_buffer_size) {
            logical_read_offset -= main_buffer_size;
        }
    }
}

3.5 Buffer Size Calculation
static size_t calculate_buffer_size(size_t chunk_len, size_t overlap, size_t max_write) {
// Minimum: hold 2 max writes + 1 chunk to prevent starvation
size_t min_size = 2 * max_write + chunk_len;

    // Optimal: 4-5x the larger of max_write or chunk_len
    size_t optimal_base = std::max(max_write, chunk_len) * 4;

    // Align to max_write boundary for efficient writes
    size_t aligned_size = ((optimal_base + max_write - 1) / max_write) * max_write;

    // Cap at reasonable maximum (1MB for 4-byte types = 256K elements)
    constexpr size_t max_elements = (1024 * 1024) / sizeof(T);

    return std::min(std::max(min_size, aligned_size), max_elements);
}

4. Complete Implementation Template
   #include <vector>
   #include <functional>
   #include <tuple>
   #include <algorithm>
   #include <cassert>
   #include <type_traits>

template<typename T>
class ChunkStreamProcessor {
static_assert(std::is_trivially_copyable_v<T>,
"T must be trivially copyable for buffer operations");

private:
// Configuration
const size_t chunk_length;
const size_t left_overlap;
const size_t max_write_size;
const size_t main_buffer_size;
const size_t chunk_stride;

    // Buffers
    std::vector<T> main_buffer;
    std::vector<T> glue_buffer;

    // State
    size_t write_pos = 0;
    size_t used_space = 0;
    size_t logical_read_offset = 0;
    bool first_chunk_pending = true;

    std::function<void(const T*, size_t)> chunk_callback;

public:
    ChunkStreamProcessor(size_t chunk_len, size_t overlap, size_t max_write)
        : chunk_length(chunk_len)
        , left_overlap(overlap)
        , max_write_size(max_write)
        , main_buffer_size(calculate_buffer_size(chunk_len, overlap, max_write))
        , chunk_stride(chunk_len - overlap)
        , main_buffer(main_buffer_size)
        , glue_buffer(chunk_len)
    {
        assert(chunk_len > overlap);
        assert(max_write > 0);
    }

    void set_callback(std::function<void(const T*, size_t)> callback) {
        chunk_callback = std::move(callback);
    }

    std::tuple<T*, size_t> get_write_pointer() {
        size_t free_space = main_buffer_size - used_space;
        if (free_space == 0) {
            return {nullptr, 0};
        }

        size_t space_to_end = main_buffer_size - write_pos;
        size_t writable_size = std::min({free_space, space_to_end, max_write_size});

        return {&main_buffer[write_pos], writable_size};
    }

    void commit_write(size_t written_elements) {
        assert(written_elements <= max_write_size);
        assert(used_space + written_elements <= main_buffer_size);

        write_pos = (write_pos + written_elements) % main_buffer_size;
        used_space += written_elements;

        process_available_chunks();
    }

    size_t get_available_space() const {
        return main_buffer_size - used_space;
    }

    size_t get_buffered_elements() const {
        return used_space;
    }

    bool can_write() const {
        return used_space < main_buffer_size;
    }

private:
static size_t calculate_buffer_size(size_t chunk_len, size_t overlap, size_t max_write) {
size_t min_size = 2 * max_write + chunk_len;
size_t optimal_base = std::max(max_write, chunk_len) * 4;
size_t aligned_size = ((optimal_base + max_write - 1) / max_write) * max_write;
constexpr size_t max_elements = (1024 * 1024) / sizeof(T);

        return std::min(std::max(min_size, aligned_size), max_elements);
    }

    void process_available_chunks() {
        while (can_form_chunk()) {
            const T* chunk_ptr = get_chunk_pointer();
            chunk_callback(chunk_ptr, chunk_length);
            advance_to_next_chunk();
        }
    }

    bool can_form_chunk() const {
        if (first_chunk_pending) {
            return used_space >= chunk_length;
        } else {
            return (used_space - logical_read_offset) >= chunk_length;
        }
    }

    size_t calculate_physical_read_pos() const {
        return (write_pos + main_buffer_size - used_space + logical_read_offset)
               % main_buffer_size;
    }

    const T* get_chunk_pointer() {
        size_t read_pos = calculate_physical_read_pos();

        if (read_pos + chunk_length <= main_buffer_size) {
            return &main_buffer[read_pos];
        } else {
            size_t first_part = main_buffer_size - read_pos;
            size_t second_part = chunk_length - first_part;

            std::copy_n(&main_buffer[read_pos], first_part, glue_buffer.begin());
            std::copy_n(&main_buffer[0], second_part, glue_buffer.begin() + first_part);

            return glue_buffer.data();
        }
    }

    void advance_to_next_chunk() {
        if (first_chunk_pending) {
            first_chunk_pending = false;
            logical_read_offset = chunk_stride;
            used_space -= chunk_length;
        } else {
            logical_read_offset += chunk_stride;
            used_space -= chunk_stride;

            while (logical_read_offset >= main_buffer_size) {
                logical_read_offset -= main_buffer_size;
            }
        }
    }
};

5. Special Cases and Edge Conditions
   5.1 First Chunk Handling

The first chunk cannot have a left overlap from previous data. The implementation handles this by:

Setting first_chunk_pending = true initially
For the first chunk, consuming exactly chunk_length elements
Setting logical_read_offset = chunk_stride after first chunk
Subsequent chunks advance by chunk_stride elements
5.2 Buffer Wraparound

When a chunk spans the buffer boundary:

Use the pre-allocated glue_buffer
Copy the two parts into glue_buffer
Return glue_buffer pointer to consumer
5.3 Buffer Full Condition

When get_write_pointer() returns {nullptr, 0}:

The caller must handle this condition
Options: skip data, wait for consumption, or force processing
5.4 Partial Writes

If the source provides less than max_write_size:

Still valid as long as it fits in available space
Process chunks immediately if possible
6. Thread Safety and Constraints
   Single-threaded: Not thread-safe by design
   Synchronous callbacks: Consumer must process chunks synchronously
   No pointer validity guarantee: Chunk pointers invalid after callback returns
   Type requirements: T must be trivially copyable
7. Usage Example
   // Define data type and parameters
   using SampleType = int32_t;
   const size_t chunk_size = 200;
   const size_t overlap = 50;
   const size_t rx_buffer_size = 2048;

// Create processor
ChunkStreamProcessor<SampleType> processor(chunk_size, overlap, rx_buffer_size);

// Set up callback
processor.set_callback([](const SampleType* data, size_t length) {
// Process chunk (length always equals chunk_size)
process_audio_chunk(data, length);
});

// Main processing loop
while (running) {
// Get write location
auto [write_ptr, available] = processor.get_write_pointer();

    if (write_ptr && available > 0) {
        // Receive data directly into buffer
        int status = bladerf_sync_rx(device, write_ptr, available, nullptr, timeout);

        if (status == 0) {
            // Commit the write and process chunks
            processor.commit_write(available);
        }
    }
}

8. Testing Considerations
   Test Cases
   Basic operation: Write exactly chunk_length elements, verify one chunk produced
   Overlap verification: Write multiple chunks worth, verify overlap content
   Wraparound: Fill buffer to force wraparound, verify chunk integrity
   Buffer full: Fill buffer completely, verify get_write_pointer returns {nullptr, 0}
   Various sizes: Test with different chunk_length/overlap combinations
   First chunk: Verify first chunk handling
   Invariants to Verify
   write_pos < main_buffer_size always
   used_space <= main_buffer_size always
   logical_read_offset < used_space when chunks are available
   Chunk data matches expected overlap pattern
   No memory leaks or buffer overruns
9. Performance Considerations
   Zero-copy: Most chunks should use direct pointers (monitor glue_buffer usage)
   Cache efficiency: Circular buffer should fit in L3 cache when possible
   Callback overhead: Keep callbacks lightweight as they block further reception
   Memory allocation: All buffers pre-allocated, no dynamic allocation in hot path
10. Integration Notes
    BladeRF Integration
    class BladeRFChunkProcessor {
    private:
    ChunkStreamProcessor<int32_t> processor;

public:
    BladeRFChunkProcessor(size_t chunk_size, size_t overlap, size_t rx_size)
        : processor(chunk_size, overlap, rx_size) {}

    void receive_and_process(bladerf* device, unsigned int timeout_ms) {
        auto [write_ptr, available] = processor.get_write_pointer();

        if (write_ptr && available > 0) {
            int status = bladerf_sync_rx(device, write_ptr, available, nullptr, timeout_ms);
            if (status == 0) {
                processor.commit_write(available);
            }
        }
    }
};


This specification provides complete implementation details for creating a ChunkStreamProcessor with the exact behavior and optimizations discussed.