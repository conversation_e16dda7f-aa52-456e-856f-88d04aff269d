# Pipeline Architecture Refactoring Summary

## Overview
This document summarizes the major refactoring of the pipeline architecture to improve naming, component responsibilities, and separation of concerns. The refactoring implements a simplified single-instance processing design with better separation between stream creation and video processing.

## Key Changes

### 1. **Simplified Single-Instance Processing**
- **Before**: Global `g_pipelines` map managing multiple pipeline instances with handle IDs
- **After**: Single global `g_videoConverter` instance for simplified single-instance design
- **Rationale**: Since there will only be one active processing stream per process, removed unnecessary complexity around multiple pipeline management

### 2. **Stream Creation Factory Pattern**
- **New**: `IQStreamFactory` class in `src/stream-factory/`
- **Responsibilities**:
  - Handle all stream creation logic for WAV and BladeRF sources
  - Perform stream-specific configuration validation
  - Return initialized and validated IIQStream instances
  - Provide clear error messages for configuration or initialization failures
- **Benefits**: Separates stream creation concerns from video processing logic

### 3. **Separated Stream and Processing Configurations**
- **Before**: Monolithic `PipelineManager::Config` with nested `SourceConfig` and `ProcessingConfig`
- **After**: 
  - `IQStreamFactory::StreamConfig` for stream creation parameters
  - `VideoStreamConverter::VideoProcessingConfig` for video processing parameters
- **Benefits**: Allows creating IIQStream in Node.js thread with early validation

### 4. **Improved Naming Throughout Application**
- **Class Names**:
  - `PipelineManager` → `VideoStreamConverter`
  - `PipelineManager::Handle` → `VideoStreamConverter::Handle` (simplified)
  - `VideoPipelineDemo` → `VideoConverterDemo`
- **JavaScript Interface**:
  - `startPipeline()` → `createVideoConverter()`
  - `stopPipeline()` → `stopVideoConverter()`
  - `getPipelineStats()` → `getVideoConverterStats()`
  - Added `isVideoConverterRunning()`
- **Configuration Objects**:
  - `SourceConfig` → `StreamConfig` with typed sub-configs
  - `ProcessingConfig` → `VideoProcessingConfig`

## New File Structure

### Added Files
```
src/stream-factory/
├── iq_stream_factory.h      # Factory interface for IQ stream creation
└── iq_stream_factory.cpp    # Factory implementation

src/video-converter/
├── video_stream_converter.h   # Main video conversion orchestrator
└── video_stream_converter.cpp # Video conversion implementation
```

### Modified Files
- `src/addon.cpp` - Updated JavaScript interface with new API
- `demo.js` - Updated to use new VideoConverterDemo class and API
- `test_basic.js` - Updated to use new createVideoConverter API
- `binding.gyp` - Added new source files and include directories

## API Changes

### JavaScript Interface (Before)
```javascript
const handle = addon.startPipeline(config, frameCallback);
const success = addon.stopPipeline(handle.id);
const stats = addon.getPipelineStats(handle.id);
```

### JavaScript Interface (After)
```javascript
const result = addon.createVideoConverter(config, frameCallback);
const success = addon.stopVideoConverter();
const stats = addon.getVideoConverterStats();
const running = addon.isVideoConverterRunning();
```

### Configuration Structure (Before)
```javascript
const config = {
  source: {
    type: "wav",
    path: "/path/to/file.wav",
    playMode: "realtime"
  },
  processing: {
    centerOffsetHz: 0,
    sliceStrategy: "auto_ntsc",
    frameWidthPx: 640,
    queueDepth: { raw: 128, demod: 128, lines: 64 }
  }
};
```

### Configuration Structure (After)
```javascript
// Same structure, but internally parsed into separate configs:
// - StreamConfig for IQStreamFactory
// - VideoProcessingConfig for VideoStreamConverter
```

## Benefits of Refactoring

### 1. **Clearer Responsibilities**
- `IQStreamFactory`: Stream creation and validation only
- `VideoStreamConverter`: Video processing orchestration only
- Clean separation of concerns

### 2. **Better Error Handling**
- Early stream validation in Node.js thread
- Clearer error messages from factory pattern
- No nested initialization failures

### 3. **Simplified Architecture**
- Single-instance design removes unnecessary complexity
- No global handle management
- Direct object-based API

### 4. **Improved Naming**
- "VideoConverter" clearly indicates IQ-to-video conversion purpose
- Method names reflect actual functionality
- Configuration objects have descriptive names

### 5. **Enhanced Maintainability**
- Modular design with clear boundaries
- Factory pattern allows easy extension for new stream types
- Simplified testing and debugging

## Migration Guide

### For JavaScript Code
1. Replace `startPipeline()` calls with `createVideoConverter()`
2. Replace `stopPipeline(handleId)` calls with `stopVideoConverter()`
3. Replace `getPipelineStats(handleId)` calls with `getVideoConverterStats()`
4. Update class names from `VideoPipelineDemo` to `VideoConverterDemo`
5. Remove handle ID management (no longer needed)

### For C++ Code
1. Include new headers: `stream-factory/iq_stream_factory.h` and `video-converter/video_stream_converter.h`
2. Use `IQStreamFactory::createStream()` for stream creation
3. Use `VideoStreamConverter` instead of `PipelineManager`
4. Update configuration structures to use new types

## Backward Compatibility
This is a breaking change that requires updating all client code. The old `PipelineManager` API is no longer available. However, the configuration structure remains largely the same for easier migration.

## Testing
- Update all test files to use new API
- Verify single-instance behavior works correctly
- Test stream creation validation in factory
- Confirm error handling improvements

## Future Enhancements
- Add support for additional stream types through factory pattern
- Implement proper video processing pipeline (stages 3-6)
- Add configuration validation for video processing parameters
- Consider adding stream reconnection capabilities
